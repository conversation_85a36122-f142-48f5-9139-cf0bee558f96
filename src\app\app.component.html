<!-- Only show UBZ content when show is true -->
<ng-container *ngIf="show">
  <header>
    <app-head-menu></app-head-menu>
  </header>
  <app-search-menu [isSide]="false" [isOpened]="menuService.eventIsSearchBoxOpen"></app-search-menu>
  <section id="wrapper" [class]="menuService.sideMenuPropriety">
    <app-side-menu></app-side-menu>
    <section id="page-content-wrapper" (click)="closeSideMenu()">
      <div class="container-fluid">
        <router-outlet></router-outlet>
      </div>
    </section>
  </section>
</ng-container>

<!-- Show spinner during loading or redirect -->
<app-spinner [ngClass]="{'active': !show && applicationCode === 'ZA0'}"></app-spinner>