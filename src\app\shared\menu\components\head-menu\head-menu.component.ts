import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuService } from '../../services/menu.service';
import { MenuElement } from '../../model/menu-element';
import { UserData } from '../../../user-data/user-data';
import { UserDataService } from '../../../user-data/user-data.service';

@Component({
  selector: 'app-head-menu',
  templateUrl: './head-menu.component.html',
  styleUrls: ['./head-menu.component.css']
})
export class HeadMenuComponent implements OnInit {
 public userDetails:UserData;
  menuIconClass = 'menu-icon-switch icon-menu';
  userMenuClass = 'userInfo';
  username:any
  profile:any

  constructor(
    public menuService: MenuService,
    private router: Router,
    private userDataService: UserDataService) {}

  ngOnInit() {
this.userDataService.getAll().subscribe(res=>this.userDetails=res)
  }
  changeSideMenuPropriety() {
    this.menuService.changeSideMenuPropriety(null);
    if (this.menuIconClass === 'menu-icon-switch icon-close') {
      // Close side search menu
      this.menuService.isSideSearchOpen = false;
    }
    this.changeSideMenuIconClass();
  }
  private changeSideMenuIconClass() {
    if (this.menuService.sideMenuPropriety === 'toggled') {
      this.menuIconClass = 'menu-icon-switch icon-menu';
    } else {
      this.menuIconClass = 'menu-icon-switch icon-close';
    }
  }

  openCloseUserMenu() {
    if (this.userMenuClass === 'userInfo') {
      this.userMenuClass = 'userInfo open';
    } else {
      this.userMenuClass = 'userInfo';
    }
  }
  
  goToActivities() {
    this.openCloseUserMenu();
    this.router.navigate([`/activities-detail`]);
  }
}
