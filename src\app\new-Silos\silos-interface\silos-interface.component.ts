import { Component, OnInit } from "@angular/core";
import { SilosService } from "../silos.service";
import { UserDataService } from "../../shared/user-data/user-data.service";
import "rxjs/add/operator/map";
import "rxjs/add/operator/catch";

@Component({
  selector: "app-silos-interface",
  templateUrl: "./silos-interface.component.html",
  styleUrls: ["./silos-interface.component.css"],
})
export class SilosInterfaceComponent implements OnInit {
  //  new Silos Interfaces(Cerved) only
  authData: any = {
    codBanca: null,
    codFiliale: null,
    profilo: null,
    userId: null,
    hash: null,
    timestamp: null,
  };
  constructor(
    private service: SilosService,
    private userDataService: UserDataService
  ) {}

  ngOnInit() {
    this.redirectToCerved();
  }
  redirectToCerved() {
    const userId = this.userDataService.getUserData().username;
 
    console.log(userId);
    const applicationCode = this.service.appCode;
    console.log(applicationCode);
    const branchCode = this.userDataService.getUserData().branch;
    console.log(branchCode);
    this.service
      .getAuthData(userId, applicationCode, branchCode)
      .subscribe((response) => {
        console.log(response);
        if (response !== null) {
          this.authData = {
            codBanca: response.codBanca,
            codFiliale: response.codFiliale,
            profilo: response.profilo,
            userId: response.userId,
            hash: response.hash,
            timestamp: response.timestamp,
          };
          console.log(this.authData);
          this.postDataToSilos();
        }
      });
  }

  postDataToSilos() {
    this.service.dataToSilos(this.authData);
  }
  // new Silos Interfaces(Cerved) 
}